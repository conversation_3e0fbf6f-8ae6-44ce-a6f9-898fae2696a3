pluginManagement {
  repositories {
    gradlePluginPortal()
    maven {
        url 's3://repo.teqplay.nl/release'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven {
        url 's3://repo.teqplay.nl/snapshot'
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
  }
}

rootProject.name = 'vesselvoyagecompletenesstest-backend'
