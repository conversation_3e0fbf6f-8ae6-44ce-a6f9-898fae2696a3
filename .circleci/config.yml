defaults: &defaults
  working_directory: ~/repo
  docker:
    - image: cimg/openjdk:17.0

version: 2.1
jobs:
  build:
    <<: *defaults
    steps:
      - checkout_and_restore_cache
      - add_credentials
      - update_cache
      - verify_kotlin_code
      - test_and_build
      - persist_to_workspace:
          root: ~/repo
          paths:
            - build
            - ./*/build
            - out
            - version.properties
            - build.env

  deploy:
    <<: *defaults
    docker:
      - image: cimg/aws:2024.03
    parameters:
      cluster-name:
        description: Name of the EKS cluster
        type: string
      develop:
        description: If it's about a develop release
        type: boolean
    steps:
      - checkout
      - add_credentials
      - attach_workspace:
          at: ~/repo
      - setup_remote_docker
      - publish_container_image
      - deploy_to_kubernetes:
          cluster-name: << parameters.cluster-name >>
          develop: << parameters.develop >>

workflows:
  version: 2
  build-approve-deploy:
    jobs:
      - build:
          context: common-builds-context
      - hold-develop:
          type: approval
          requires:
            - build
      - deploy:
          name: deploy-develop
          cluster-name: develop
          develop: true
          requires:
            - hold-develop
          context: common-builds-context
          filters:
            branches:
              ignore:
                - master
      - hold-production:
          type: approval
          requires:
            - build
          filters:
            branches:
              only:
                - master
      - deploy:
          name: deploy-production
          cluster-name: production
          develop: false
          requires:
            - hold-production
          context: common-builds-context

commands:
  checkout_and_restore_cache:
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "build.gradle" }}
            # fallback to using the latest cache if no exact match is found
            - v1-dependencies-
  update_cache:
    steps:
      - run: ./gradlew dependencies
      - save_cache:
          paths:
            - ~/.gradle
          key: v1-dependencies-{{ checksum "build.gradle" }}
  verify_kotlin_code:
    steps:
      - run: ./gradlew ktlintCheck
  test_and_build:
    steps:
      - run:
          name: Test and build
          command: |
            echo "export BUILD_NUM=$CIRCLE_BUILD_NUM" >> build.env
            source build.env
            
            ./gradlew test build
            
            echo "export TAG=$(./gradlew -q getVersion | tail -n 1)" >> build.env
      - run:
          name: Save test results
          command: |
            mkdir -p ~/tests/junit
            find . -type f -regex ".*/build/test-results/.*xml" -exec cp {} ~/tests/junit/ \;
          when: always

      - store_test_results:
          path: ~/tests
  add_credentials:
    steps:
      - run:
          name: Add S3 credentials
          command: |
            PROPFILE=~/.gradle/gradle.properties
            mkdir -p `dirname $PROPFILE`
            [ -e $PROPFILE ] || touch $PROPFILE
            grep -q s3_access_key $PROPFILE || echo "s3_access_key = $AWS_ACCESS_KEY" >> $PROPFILE
            grep -q s3_secret_key $PROPFILE || echo "s3_secret_key = $AWS_SECRET_KEY" >> $PROPFILE
      - run:
          name: Add AWS credentials
          command: |
            FILE=~/.aws/credentials
            mkdir -p `dirname $FILE`
            echo "[default]" > $FILE
            echo "aws_access_key_id = $AWS_ACCESS_KEY" >> $FILE
            echo "aws_secret_access_key = $AWS_SECRET_KEY" >> $FILE
  publish_container_image:
    steps:
      - run:
          name: Build docker image
          command: |
            source build.env
            ./gradlew bootBuildImage
      - run:
          name: Publish docker image to ECR repository
          command: |
            source build.env
            ./gradlew dockerPushImage
  deploy_to_kubernetes:
    parameters:
      cluster-name:
        description: Name of the EKS cluster
        type: string
      develop:
        description: If it's about a develop release
        type: boolean
    steps:
      - run:
          name: Init kubeconfig
          command: |
            aws eks update-kubeconfig --name << parameters.cluster-name >> --alias << parameters.cluster-name >> --region $AWS_REGION
      - run:
          name: Init helm-charts repo
          command: |
            helm repo add teqplay https://chartmuseum.teqplay.nl --username $CM_USERNAME --password $CM_PASSWORD
            helm repo update
      - run:
          command: |
            source build.env
            
            REPONAME=$(basename $CIRCLE_PROJECT_REPONAME -backend)
            ENV="${REPONAME,,}"
            HELM_VALUES=./helm/values.prod.yaml

            if << parameters.develop >>; then
              ENV=${ENV}-dev
              HELM_VALUES=./helm/values.dev.yaml
            fi

            echo "$ENV > $TAG"

            helm upgrade $ENV teqplay/skeleton-mongo-app -n teqplay-app -f ./helm/values.yaml -f $HELM_VALUES --set image.tag=$TAG