import com.bmuschko.gradle.docker.tasks.image.DockerPushImage

buildscript {
    ext {
        // Base version of the application
        base_version = "0.0.1-SNAPSHOT"
        kotlin_version = "1.8.10"
        skeleton_version = "2.2.6-b1556"
        spring_boot_version = "3.1.2"
        spring_cloud_version = "3.0.5"
        kotlin_logging_version = '7.0.4'
        mockito_kotlin_version = "4.0.0"
        mongodb_version = '4.11.0'
        sonatype_version = "2.2.3"
        ktlint_version = "10.2.1"
        docker_api_version = "7.4.0"
        ecr_version = "0.7.0"
        ecr_repo_url = "050356841556.dkr.ecr.eu-west-1.amazonaws.com"
        vesselvoyage_version = "20250703-b660.1"
    }
    ext['mongodb.version'] = ext.mongodb_version

    repositories {
        mavenCentral()
        gradlePluginPortal()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.springframework.boot:spring-boot-gradle-plugin:$spring_boot_version"
        classpath "org.jetbrains.kotlin:kotlin-allopen:$kotlin_version"
        classpath "org.sonatype.gradle.plugins:scan-gradle-plugin:$sonatype_version"
        classpath "org.jlleitschuh.gradle:ktlint-gradle:$ktlint_version"
    }
}

plugins {
    id "com.adarshr.test-logger" version "3.0.0"
    id "com.github.ben-manes.versions" version "0.39.0"
    id "com.bmuschko.docker-remote-api" version "$docker_api_version"
    id "com.patdouble.awsecr" version "$ecr_version"
}

group "nl.teqplay.vesselvoyagecompletenesstest"
version = generateVersion()

repositories {
    mavenCentral()
    maven { url "https://jitpack.io" }
    maven {
        url "s3://repo.teqplay.nl/release"
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
    maven {
        url "s3://repo.teqplay.nl/snapshot"
        credentials(AwsCredentials) {
            accessKey "$s3_access_key"
            secretKey "$s3_secret_key"
        }
    }
}

apply plugin: "kotlin"
apply plugin: "kotlin-spring"
apply plugin: "org.springframework.boot"
apply plugin: "io.spring.dependency-management"
apply plugin: "war"
apply plugin: "org.sonatype.gradle.plugins.scan"
apply plugin: "org.jlleitschuh.gradle.ktlint"

dependencies {
    implementation "nl.teqplay.skeleton:common:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-network:$skeleton_version"
    implementation "nl.teqplay.skeleton:datasource2:$skeleton_version"

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"

    implementation "io.github.oshai:kotlin-logging-jvm:$kotlin_logging_version"
    implementation "ch.qos.logback:logback-classic"
    implementation "org.codehaus.janino:janino"

    implementation "org.springframework.boot:spring-boot-starter"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.boot:spring-boot-starter-test"

    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$spring_cloud_version"

    implementation "com.fasterxml.jackson.core:jackson-databind"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"

    testImplementation "org.junit.jupiter:junit-jupiter-api"
    testImplementation "org.junit.jupiter:junit-jupiter-params"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine"

    testImplementation "org.mockito.kotlin:mockito-kotlin:$mockito_kotlin_version"
    implementation "nl.teqplay.skeleton:rabbitmq-builder:$skeleton_version"
    implementation "nl.teqplay.vesselvoyage:api:$vesselvoyage_version"
}

tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).all {
    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs = ["-Xjsr305=strict"]
    }
}

test {
    useJUnitPlatform {
        includeEngines "junit-jupiter"
    }
}

ossIndexAudit {
    printBanner = false
}

/**
 * EKS Deployment tasks
 */
docker {
    registryCredentials {
        url.set(ecr_repo_url)
    }
}

def dockerImageName = "$ecr_repo_url/${project.name.replace("-backend", "")}:${project.version}"

bootBuildImage {
    imageName = dockerImageName
}

task dockerPushImage(type: DockerPushImage) {
    images.add(dockerImageName)
}

task getVersion {
    doLast {
        println project.version
    }
}

static def generateVersion() {
    if (System.getenv("GH") != null) {
        def branch = System.getenv("BRANCH").replace("/", "_").toLowerCase()
        def buildNum = System.getenv("GITHUB_RUN_NUMBER")
        def formattedTimestamp = getTimestamp()
        return branch + "-" + formattedTimestamp + "-b" + buildNum
    } else {
        return "local"
    }
}

static def getTimestamp() {
    def date = new Date()
    def formattedDate = date.format('yyyy-MM-dd')
    return formattedDate
}