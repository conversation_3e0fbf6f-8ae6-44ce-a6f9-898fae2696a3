package nl.teqplay.vesselvoyagecompletenesstest

import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestPropertySource

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = [
    "spring.data.mongodb.uri=mongodb://localhost:27017/test",
    "spring.rabbitmq.host=localhost",
    "spring.rabbitmq.port=5672",
    "app.rabbitmq.queue.vessel-events=vessel-events-test"
])
class ApplicationTest : BaseTest() {
    @Test
    fun contextLoads() {
        // This test verifies that the Spring context loads successfully with all configurations
    }
}
