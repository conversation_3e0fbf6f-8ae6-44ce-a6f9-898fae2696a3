package nl.teqplay.vesselvoyagecompletenesstest.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.model.VisitCompleteness
import org.bson.types.ObjectId

class VisitCompletenessDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<VisitCompleteness>("visitCompleteness")

    fun save(visit: VisitCompleteness) {
        collection.save(visit)
    }

    fun delete(visitId: String) {
        collection.deleteOneById(visitId)
    }

    fun list(): List<VisitCompleteness> {
        return collection.find().toList()
    }

    fun findById(id: String): VisitCompleteness? {
        return collection.find(com.mongodb.client.model.Filters.eq("_id", id)).firstOrNull()
    }
}