package nl.teqplay.vesselvoyagecompletenesstest.service

import nl.teqplay.vesselvoyage.apiv2.model.OutgoingSofChange
import nl.teqplay.vesselvoyage.apiv2.model.sof.pto.PtoStatementOfFactsView
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyage.model.event.EncounterType
import nl.teqplay.vesselvoyagecompletenesstest.datasource.SOFCompletenessDataSource
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness

class SOFChangeHandler(private val sofCompletenessDataSource: SOFCompletenessDataSource) {
    fun process(entry: OutgoingSofChange) {
        when(entry.value) {
            is PtoStatementOfFactsView -> {
                when(entry.action) {
                    Action.CREATE, Action.UPDATE, Action.REVISE -> {
                        val completeness = getCompleteness(entry.value as PtoStatementOfFactsView)
                        save(completeness)
                    }
                    Action.DELETE, Action.DISCONTINUE -> {
                        delete(entry.value.entryId)
                    }
                }
            }
            else -> return
        }
    }

    private fun getCompleteness(sof: PtoStatementOfFactsView): SOFCompleteness {
        return SOFCompleteness(
            _id = sof.entryId,
            pilotInboundPresent = sof.pilotInbound != null,
            pilotOutboundPresent = sof.pilotOutbound != null,
            anyTugPresent = sof.encounters.any { it.type == EncounterType.TUG.toString() }
        )
    }

    private fun save(completeness: SOFCompleteness) {
        sofCompletenessDataSource.save(completeness)
    }

    private fun delete(id: String) {
        sofCompletenessDataSource.delete(id)
    }
}