package nl.teqplay.vesselvoyagecompletenesstest.service

import nl.teqplay.vesselvoyage.apiv2.model.OutgoingEntryChange
import nl.teqplay.vesselvoyage.apiv2.model.Visit
import nl.teqplay.vesselvoyage.apiv2.model.Voyage
import nl.teqplay.vesselvoyage.model.Action
import nl.teqplay.vesselvoyagecompletenesstest.datasource.VisitCompletenessDataSource
import nl.teqplay.vesselvoyagecompletenesstest.model.VisitCompleteness

class EntryChangeHandler(
    private val visitCompletenessDataSource: VisitCompletenessDataSource,
) {

    fun process(entry: OutgoingEntryChange) {
        when(entry.value) {
            is Visit -> {
                when(entry.action) {
                    Action.CREATE, Action.UPDATE, Action.REVISE -> {
                        val completeness = getCompleteness(entry.value as Visit)
                        save(completeness)
                    }
                    Action.DELETE, Action.DISCONTINUE -> {
                        delete(entry.value.entryId)
                    }
                }
            }
            is Voyage -> {
                // Only visits are processed
                return
            }
        }
    }

    fun save(completeness: VisitCompleteness) {
        visitCompletenessDataSource.save(completeness)
    }

    fun delete(id: String) {
        visitCompletenessDataSource.delete(id)
    }

    fun getCompleteness(visit: Visit): VisitCompleteness {
        return VisitCompleteness(
            _id = visit.entryId,
            startEndTimestampPresent = visit.end != null,
            mainPortAreaStartEndTimestampPresent = isPortTimePresent(visit.port.main, visit.portTimes),
            subPortAreaStartEndTimestampPresent = visit.port.sub.all { isPortTimePresent(it, visit.portTimes) },
        )
    }

    private fun isPortTimePresent(port: String, portTimes: List<Visit.PortTime>): Boolean {
        return portTimes.any { it.id == port && it.end != null }
    }

}