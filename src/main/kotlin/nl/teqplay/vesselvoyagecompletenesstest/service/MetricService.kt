package nl.teqplay.vesselvoyagecompletenesstest.service

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.vesselvoyagecompletenesstest.model.VisitCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness

class MetricService(meterRegistry: MeterRegistry) {
    companion object {
        private const val TAG_INPUT_TYPE_KEY = "input_type"
        private const val TAG_INPUT_TYPE_VISIT = "visit"
        private const val TAG_INPUT_TYPE_SOF = "sof"
    }
    
    val totalVisits = meterRegistry.counter("total_visits", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisits = meterRegistry.counter("complete_visits", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val totalVisitStartEndTime = meterRegistry.counter("total_visit_start_end_time", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val totalVisitMainPortStartEnd = meterRegistry.counter("total_visit_main_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val totalVisitSubPortStartEnd = meterRegistry.counter("total_visit_sub_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisitStartEndTime = meterRegistry.counter("complete_visit_start_end_time", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisitMainPortStartEnd = meterRegistry.counter("complete_visit_main_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisitSubPortStartEnd = meterRegistry.counter("complete_visit_sub_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)

    val totalSOFs = meterRegistry.counter("total_sofs", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFs = meterRegistry.counter("complete_sofs", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val totalSOFPilotInbound = meterRegistry.counter("total_sof_pilot_inbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val totalSOFPilotOutbound = meterRegistry.counter("total_sof_pilot_outbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val totalSOFAnyTugPresent = meterRegistry.counter("total_sof_any_tug_present", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFPilotInbound = meterRegistry.counter("complete_sof_pilot_inbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFPilotOutbound = meterRegistry.counter("complete_sof_pilot_outbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFAnyTugPresent = meterRegistry.counter("complete_sof_any_tug_present", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)

    /**
     * Updates meter registries based on VisitCompleteness data
     */
    fun updateVisitMetrics(visitCompleteness: VisitCompleteness) {
        // Increment total visits counter
        totalVisits.increment()

        // Update start/end time metrics
        totalVisitStartEndTime.increment()
        if (visitCompleteness.startEndTimestampPresent) {
            completeVisitStartEndTime.increment()
        }

        // Update main port start/end metrics
        totalVisitMainPortStartEnd.increment()
        if (visitCompleteness.mainPortAreaStartEndTimestampPresent) {
            completeVisitMainPortStartEnd.increment()
        }

        // Update sub port start/end metrics
        totalVisitSubPortStartEnd.increment()
        if (visitCompleteness.subPortAreaStartEndTimestampPresent) {
            completeVisitSubPortStartEnd.increment()
        }

        // Check if visit is complete (all required fields present)
        val isComplete = visitCompleteness.startEndTimestampPresent &&
                        visitCompleteness.mainPortAreaStartEndTimestampPresent &&
                        visitCompleteness.subPortAreaStartEndTimestampPresent

        if (isComplete) {
            completeVisits.increment()
        }
    }

    /**
     * Updates meter registries based on SOFCompleteness data
     */
    fun updateSOFMetrics(sofCompleteness: SOFCompleteness) {
        // Increment total SOFs counter
        totalSOFs.increment()

        // Update pilot inbound metrics
        totalSOFPilotInbound.increment()
        if (sofCompleteness.pilotInboundPresent) {
            completeSOFPilotInbound.increment()
        }

        // Update pilot outbound metrics
        totalSOFPilotOutbound.increment()
        if (sofCompleteness.pilotOutboundPresent) {
            completeSOFPilotOutbound.increment()
        }

        // Update tug present metrics
        totalSOFAnyTugPresent.increment()
        if (sofCompleteness.anyTugPresent) {
            completeSOFAnyTugPresent.increment()
        }

        // Check if SOF is complete (all required fields present)
        val isComplete = sofCompleteness.pilotInboundPresent &&
                        sofCompleteness.pilotOutboundPresent &&
                        sofCompleteness.anyTugPresent

        if (isComplete) {
            completeSOFs.increment()
        }
    }
}