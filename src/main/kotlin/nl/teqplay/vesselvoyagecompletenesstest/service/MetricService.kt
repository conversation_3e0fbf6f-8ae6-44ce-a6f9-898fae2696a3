package nl.teqplay.vesselvoyagecompletenesstest.service

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.vesselvoyagecompletenesstest.model.VisitCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.datasource.VisitCompletenessDataSource
import nl.teqplay.vesselvoyagecompletenesstest.datasource.SOFCompletenessDataSource
import java.util.concurrent.atomic.AtomicLong

class MetricService(
    meterRegistry: MeterRegistry,
    private val visitCompletenessDataSource: VisitCompletenessDataSource,
    private val sofCompletenessDataSource: SOFCompletenessDataSource
) {
    companion object {
        private const val TAG_INPUT_TYPE_KEY = "input_type"
        private const val TAG_INPUT_TYPE_VISIT = "visit"
        private const val TAG_INPUT_TYPE_SOF = "sof"
    }
    
    private val totalVisitsGauge = AtomicLong(0)
    private val completeVisitsGauge = AtomicLong(0)
    private val totalVisitStartEndTimeGauge = AtomicLong(0)
    private val totalVisitMainPortStartEndGauge = AtomicLong(0)
    private val totalVisitSubPortStartEndGauge = AtomicLong(0)
    private val completeVisitStartEndTimeGauge = AtomicLong(0)
    private val completeVisitMainPortStartEndGauge = AtomicLong(0)
    private val completeVisitSubPortStartEndGauge = AtomicLong(0)

    private val totalSOFsGauge = AtomicLong(0)
    private val completeSOFsGauge = AtomicLong(0)
    private val totalSOFPilotInboundGauge = AtomicLong(0)
    private val totalSOFPilotOutboundGauge = AtomicLong(0)
    private val totalSOFAnyTugPresentGauge = AtomicLong(0)
    private val completeSOFPilotInboundGauge = AtomicLong(0)
    private val completeSOFPilotOutboundGauge = AtomicLong(0)
    private val completeSOFAnyTugPresentGauge = AtomicLong(0)

    // Register gauges with the meter registry
    init {
        meterRegistry.gauge("total_visits", totalVisitsGauge)
        meterRegistry.gauge("complete_visits", completeVisitsGauge)
        meterRegistry.gauge("total_visit_start_end_time", totalVisitStartEndTimeGauge)
        meterRegistry.gauge("total_visit_main_port_start_end", totalVisitMainPortStartEndGauge)
        meterRegistry.gauge("total_visit_sub_port_start_end", totalVisitSubPortStartEndGauge)
        meterRegistry.gauge("complete_visit_start_end_time", completeVisitStartEndTimeGauge)
        meterRegistry.gauge("complete_visit_main_port_start_end", completeVisitMainPortStartEndGauge)
        meterRegistry.gauge("complete_visit_sub_port_start_end", completeVisitSubPortStartEndGauge)

        meterRegistry.gauge("total_sofs", totalSOFsGauge)
        meterRegistry.gauge("complete_sofs", completeSOFsGauge)
        meterRegistry.gauge("total_sof_pilot_inbound", totalSOFPilotInboundGauge)
        meterRegistry.gauge("total_sof_pilot_outbound", totalSOFPilotOutboundGauge)
        meterRegistry.gauge("total_sof_any_tug_present", totalSOFAnyTugPresentGauge)
        meterRegistry.gauge("complete_sof_pilot_inbound", completeSOFPilotInboundGauge)
        meterRegistry.gauge("complete_sof_pilot_outbound", completeSOFPilotOutboundGauge)
        meterRegistry.gauge("complete_sof_any_tug_present", completeSOFAnyTugPresentGauge)
    }

    /**
     * Updates meter registries based on VisitCompleteness data, comparing with existing data
     */
    fun updateVisitMetrics(visitCompleteness: VisitCompleteness) {
        val existingVisit = visitCompletenessDataSource.findById(visitCompleteness._id)

        // If this is a new visit, increment total visits gauge
        if (existingVisit == null) {
            totalVisitsGauge.incrementAndGet()
        }

        // Update start/end time metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.startEndTimestampPresent,
            newValue = visitCompleteness.startEndTimestampPresent,
            totalGauge = totalVisitStartEndTimeGauge,
            completeGauge = completeVisitStartEndTimeGauge
        )

        // Update main port start/end metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.mainPortAreaStartEndTimestampPresent,
            newValue = visitCompleteness.mainPortAreaStartEndTimestampPresent,
            totalGauge = totalVisitMainPortStartEndGauge,
            completeGauge = completeVisitMainPortStartEndGauge
        )

        // Update sub port start/end metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.subPortAreaStartEndTimestampPresent,
            newValue = visitCompleteness.subPortAreaStartEndTimestampPresent,
            totalGauge = totalVisitSubPortStartEndGauge,
            completeGauge = completeVisitSubPortStartEndGauge
        )

        // Check if overall visit completeness changed
        val wasComplete = existingVisit?.let {
            it.startEndTimestampPresent &&
            it.mainPortAreaStartEndTimestampPresent &&
            it.subPortAreaStartEndTimestampPresent
        } ?: false

        val isComplete = visitCompleteness.startEndTimestampPresent &&
                        visitCompleteness.mainPortAreaStartEndTimestampPresent &&
                        visitCompleteness.subPortAreaStartEndTimestampPresent

        // Update complete visits gauge based on completeness change
        if (!wasComplete && isComplete) {
            completeVisitsGauge.incrementAndGet()
        } else if (wasComplete && !isComplete) {
            completeVisitsGauge.decrementAndGet()
        }
    }

    /**
     * Updates meter registries based on SOFCompleteness data, comparing with existing data
     */
    fun updateSOFMetrics(sofCompleteness: SOFCompleteness) {
        val existingSOF = sofCompletenessDataSource.findById(sofCompleteness._id)

        // If this is a new SOF, increment total SOFs gauge
        if (existingSOF == null) {
            totalSOFsGauge.incrementAndGet()
        }

        // Update pilot inbound metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.pilotInboundPresent,
            newValue = sofCompleteness.pilotInboundPresent,
            totalGauge = totalSOFPilotInboundGauge,
            completeGauge = completeSOFPilotInboundGauge
        )

        // Update pilot outbound metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.pilotOutboundPresent,
            newValue = sofCompleteness.pilotOutboundPresent,
            totalGauge = totalSOFPilotOutboundGauge,
            completeGauge = completeSOFPilotOutboundGauge
        )

        // Update tug present metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.anyTugPresent,
            newValue = sofCompleteness.anyTugPresent,
            totalGauge = totalSOFAnyTugPresentGauge,
            completeGauge = completeSOFAnyTugPresentGauge
        )

        // Check if overall SOF completeness changed
        val wasComplete = existingSOF?.let {
            it.pilotInboundPresent &&
            it.pilotOutboundPresent &&
            it.anyTugPresent
        } ?: false

        val isComplete = sofCompleteness.pilotInboundPresent &&
                        sofCompleteness.pilotOutboundPresent &&
                        sofCompleteness.anyTugPresent

        // Update complete SOFs gauge based on completeness change
        if (!wasComplete && isComplete) {
            completeSOFsGauge.incrementAndGet()
        } else if (wasComplete && !isComplete) {
            completeSOFsGauge.decrementAndGet()
        }
    }

    /**
     * Helper method to update field metrics when the field value changes
     */
    private fun updateFieldMetric(
        existingValue: Boolean?,
        newValue: Boolean,
        totalGauge: AtomicLong,
        completeGauge: AtomicLong
    ) {
        // If this is a new field (existingValue is null), always increment total
        if (existingValue == null) {
            totalGauge.incrementAndGet()
            if (newValue) {
                completeGauge.incrementAndGet()
            }
        } else {
            // Update based on value changes
            if (!existingValue && newValue) {
                // Changed from false to true
                completeGauge.incrementAndGet()
            } else if (existingValue && !newValue) {
                // Changed from true to false
                completeGauge.decrementAndGet()
            }
        }
    }



    /**
     * Deletes a visit from all metric gauges
     */
    fun deleteVisitMetrics(visitId: String) {
        val existingVisit = visitCompletenessDataSource.findById(visitId)
        if (existingVisit != null) {
            // Decrement total visits
            totalVisitsGauge.decrementAndGet()

            // Decrement field-specific totals
            totalVisitStartEndTimeGauge.decrementAndGet()
            totalVisitMainPortStartEndGauge.decrementAndGet()
            totalVisitSubPortStartEndGauge.decrementAndGet()

            // Decrement complete counters if the fields were complete
            if (existingVisit.startEndTimestampPresent) {
                completeVisitStartEndTimeGauge.decrementAndGet()
            }
            if (existingVisit.mainPortAreaStartEndTimestampPresent) {
                completeVisitMainPortStartEndGauge.decrementAndGet()
            }
            if (existingVisit.subPortAreaStartEndTimestampPresent) {
                completeVisitSubPortStartEndGauge.decrementAndGet()
            }

            // Decrement complete visits if it was complete
            val wasComplete = existingVisit.startEndTimestampPresent &&
                            existingVisit.mainPortAreaStartEndTimestampPresent &&
                            existingVisit.subPortAreaStartEndTimestampPresent
            if (wasComplete) {
                completeVisitsGauge.decrementAndGet()
            }
        }
    }

    /**
     * Deletes a SOF from all metric gauges
     */
    fun deleteSOFMetrics(sofId: String) {
        val existingSOF = sofCompletenessDataSource.findById(sofId)
        if (existingSOF != null) {
            // Decrement total SOFs
            totalSOFsGauge.decrementAndGet()

            // Decrement field-specific totals
            totalSOFPilotInboundGauge.decrementAndGet()
            totalSOFPilotOutboundGauge.decrementAndGet()
            totalSOFAnyTugPresentGauge.decrementAndGet()

            // Decrement complete counters if the fields were complete
            if (existingSOF.pilotInboundPresent) {
                completeSOFPilotInboundGauge.decrementAndGet()
            }
            if (existingSOF.pilotOutboundPresent) {
                completeSOFPilotOutboundGauge.decrementAndGet()
            }
            if (existingSOF.anyTugPresent) {
                completeSOFAnyTugPresentGauge.decrementAndGet()
            }

            // Decrement complete SOFs if it was complete
            val wasComplete = existingSOF.pilotInboundPresent &&
                            existingSOF.pilotOutboundPresent &&
                            existingSOF.anyTugPresent
            if (wasComplete) {
                completeSOFsGauge.decrementAndGet()
            }
        }
    }
}