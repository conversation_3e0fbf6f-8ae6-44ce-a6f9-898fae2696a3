package nl.teqplay.vesselvoyagecompletenesstest.service

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.vesselvoyagecompletenesstest.model.VisitCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.datasource.VisitCompletenessDataSource
import nl.teqplay.vesselvoyagecompletenesstest.datasource.SOFCompletenessDataSource

class MetricService(
    meterRegistry: MeterRegistry,
    private val visitCompletenessDataSource: VisitCompletenessDataSource,
    private val sofCompletenessDataSource: SOFCompletenessDataSource
) {
    companion object {
        private const val TAG_INPUT_TYPE_KEY = "input_type"
        private const val TAG_INPUT_TYPE_VISIT = "visit"
        private const val TAG_INPUT_TYPE_SOF = "sof"
    }
    
    val totalVisits = meterRegistry.counter("total_visits", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisits = meterRegistry.counter("complete_visits", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val totalVisitStartEndTime = meterRegistry.counter("total_visit_start_end_time", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val totalVisitMainPortStartEnd = meterRegistry.counter("total_visit_main_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val totalVisitSubPortStartEnd = meterRegistry.counter("total_visit_sub_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisitStartEndTime = meterRegistry.counter("complete_visit_start_end_time", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisitMainPortStartEnd = meterRegistry.counter("complete_visit_main_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)
    val completeVisitSubPortStartEnd = meterRegistry.counter("complete_visit_sub_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT)

    val totalSOFs = meterRegistry.counter("total_sofs", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFs = meterRegistry.counter("complete_sofs", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val totalSOFPilotInbound = meterRegistry.counter("total_sof_pilot_inbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val totalSOFPilotOutbound = meterRegistry.counter("total_sof_pilot_outbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val totalSOFAnyTugPresent = meterRegistry.counter("total_sof_any_tug_present", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFPilotInbound = meterRegistry.counter("complete_sof_pilot_inbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFPilotOutbound = meterRegistry.counter("complete_sof_pilot_outbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)
    val completeSOFAnyTugPresent = meterRegistry.counter("complete_sof_any_tug_present", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF)

    /**
     * Updates meter registries based on VisitCompleteness data, comparing with existing data
     */
    fun updateVisitMetrics(visitCompleteness: VisitCompleteness) {
        val existingVisit = visitCompletenessDataSource.findById(visitCompleteness._id)

        // If this is a new visit, increment total visits counter
        if (existingVisit == null) {
            totalVisits.increment()
        }

        // Update start/end time metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.startEndTimestampPresent,
            newValue = visitCompleteness.startEndTimestampPresent,
            totalCounter = totalVisitStartEndTime,
            completeCounter = completeVisitStartEndTime
        )

        // Update main port start/end metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.mainPortAreaStartEndTimestampPresent,
            newValue = visitCompleteness.mainPortAreaStartEndTimestampPresent,
            totalCounter = totalVisitMainPortStartEnd,
            completeCounter = completeVisitMainPortStartEnd
        )

        // Update sub port start/end metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.subPortAreaStartEndTimestampPresent,
            newValue = visitCompleteness.subPortAreaStartEndTimestampPresent,
            totalCounter = totalVisitSubPortStartEnd,
            completeCounter = completeVisitSubPortStartEnd
        )

        // Check if overall visit completeness changed
        val wasComplete = existingVisit?.let {
            it.startEndTimestampPresent &&
            it.mainPortAreaStartEndTimestampPresent &&
            it.subPortAreaStartEndTimestampPresent
        } ?: false

        val isComplete = visitCompleteness.startEndTimestampPresent &&
                        visitCompleteness.mainPortAreaStartEndTimestampPresent &&
                        visitCompleteness.subPortAreaStartEndTimestampPresent

        // Only increment complete visits if it wasn't complete before but is now
        if (!wasComplete && isComplete) {
            completeVisits.increment()
        }

        if(wasComplete && !isComplete) {
            completeVisits.
        }
    }

    /**
     * Updates meter registries based on SOFCompleteness data, comparing with existing data
     */
    fun updateSOFMetrics(sofCompleteness: SOFCompleteness) {
        val existingSOF = sofCompletenessDataSource.findById(sofCompleteness._id)

        // If this is a new SOF, increment total SOFs counter
        if (existingSOF == null) {
            totalSOFs.increment()
        }

        // Update pilot inbound metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.pilotInboundPresent,
            newValue = sofCompleteness.pilotInboundPresent,
            totalCounter = totalSOFPilotInbound,
            completeCounter = completeSOFPilotInbound
        )

        // Update pilot outbound metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.pilotOutboundPresent,
            newValue = sofCompleteness.pilotOutboundPresent,
            totalCounter = totalSOFPilotOutbound,
            completeCounter = completeSOFPilotOutbound
        )

        // Update tug present metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.anyTugPresent,
            newValue = sofCompleteness.anyTugPresent,
            totalCounter = totalSOFAnyTugPresent,
            completeCounter = completeSOFAnyTugPresent
        )

        // Check if overall SOF completeness changed
        val wasComplete = existingSOF?.let {
            it.pilotInboundPresent &&
            it.pilotOutboundPresent &&
            it.anyTugPresent
        } ?: false

        val isComplete = sofCompleteness.pilotInboundPresent &&
                        sofCompleteness.pilotOutboundPresent &&
                        sofCompleteness.anyTugPresent

        // Only increment complete SOFs if it wasn't complete before but is now
        if (!wasComplete && isComplete) {
            completeSOFs.increment()
        }
    }

    /**
     * Helper method to update visit field metrics only when the field value changes
     */
    private fun updateFieldMetric(
        existingValue: Boolean?,
        newValue: Boolean,
        totalCounter: io.micrometer.core.instrument.Counter,
        completeCounter: io.micrometer.core.instrument.Counter
    ) {
        // If this is a new field (existingValue is null), always increment total
        if (existingValue == null) {
            totalCounter.increment()
            if (newValue) {
                completeCounter.increment()
            }
        } else {
            // Only update if the value changed from false to true
            if (!existingValue && newValue) {
                completeCounter.increment()
            }
        }
    }
}