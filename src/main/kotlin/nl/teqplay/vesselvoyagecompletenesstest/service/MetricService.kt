package nl.teqplay.vesselvoyagecompletenesstest.service

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.vesselvoyagecompletenesstest.model.VisitCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.model.SOFCompleteness
import nl.teqplay.vesselvoyagecompletenesstest.datasource.VisitCompletenessDataSource
import nl.teqplay.vesselvoyagecompletenesstest.datasource.SOFCompletenessDataSource

class MetricService(
    meterRegistry: MeterRegistry,
    private val visitCompletenessDataSource: VisitCompletenessDataSource,
    private val sofCompletenessDataSource: SOFCompletenessDataSource
) {
    companion object {
        private const val TAG_INPUT_TYPE_KEY = "input_type"
        private const val TAG_INPUT_TYPE_VISIT = "visit"
        private const val TAG_INPUT_TYPE_SOF = "sof"
    }
    
    val totalVisits = meterRegistry.gauge("total_visits", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val completeVisits = meterRegistry.gauge("complete_visits", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val totalVisitStartEndTime = meterRegistry.gauge("total_visit_start_end_time", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val totalVisitMainPortStartEnd = meterRegistry.gauge("total_visit_main_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val totalVisitSubPortStartEnd = meterRegistry.gauge("total_visit_sub_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val completeVisitStartEndTime = meterRegistry.gauge("complete_visit_start_end_time", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val completeVisitMainPortStartEnd = meterRegistry.gauge("complete_visit_main_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))
    val completeVisitSubPortStartEnd = meterRegistry.gauge("complete_visit_sub_port_start_end", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_VISIT, java.util.concurrent.atomic.AtomicLong(0))

    val totalSOFs = meterRegistry.gauge("total_sofs", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val completeSOFs = meterRegistry.gauge("complete_sofs", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val totalSOFPilotInbound = meterRegistry.gauge("total_sof_pilot_inbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val totalSOFPilotOutbound = meterRegistry.gauge("total_sof_pilot_outbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val totalSOFAnyTugPresent = meterRegistry.gauge("total_sof_any_tug_present", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val completeSOFPilotInbound = meterRegistry.gauge("complete_sof_pilot_inbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val completeSOFPilotOutbound = meterRegistry.gauge("complete_sof_pilot_outbound", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))
    val completeSOFAnyTugPresent = meterRegistry.gauge("complete_sof_any_tug_present", TAG_INPUT_TYPE_KEY, TAG_INPUT_TYPE_SOF, java.util.concurrent.atomic.AtomicLong(0))

    /**
     * Updates meter registries based on VisitCompleteness data, comparing with existing data
     */
    fun updateVisitMetrics(visitCompleteness: VisitCompleteness) {
        val existingVisit = visitCompletenessDataSource.findById(visitCompleteness._id)

        // If this is a new visit, increment total visits gauge
        if (existingVisit == null) {
            totalVisits!!.incrementAndGet()
        }

        // Update start/end time metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.startEndTimestampPresent,
            newValue = visitCompleteness.startEndTimestampPresent,
            totalCounter = totalVisitStartEndTime,
            completeCounter = completeVisitStartEndTime
        )

        // Update main port start/end metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.mainPortAreaStartEndTimestampPresent,
            newValue = visitCompleteness.mainPortAreaStartEndTimestampPresent,
            totalCounter = totalVisitMainPortStartEnd,
            completeCounter = completeVisitMainPortStartEnd
        )

        // Update sub port start/end metrics only if the field changed
        updateFieldMetric(
            existingValue = existingVisit?.subPortAreaStartEndTimestampPresent,
            newValue = visitCompleteness.subPortAreaStartEndTimestampPresent,
            totalCounter = totalVisitSubPortStartEnd,
            completeCounter = completeVisitSubPortStartEnd
        )

        // Check if overall visit completeness changed
        val wasComplete = existingVisit?.let {
            it.startEndTimestampPresent &&
            it.mainPortAreaStartEndTimestampPresent &&
            it.subPortAreaStartEndTimestampPresent
        } ?: false

        val isComplete = visitCompleteness.startEndTimestampPresent &&
                        visitCompleteness.mainPortAreaStartEndTimestampPresent &&
                        visitCompleteness.subPortAreaStartEndTimestampPresent

        // Update complete visits gauge based on completeness change
        if (!wasComplete && isComplete) {
            completeVisits!!.incrementAndGet()
        } else if (wasComplete && !isComplete) {
            completeVisits!!.decrementAndGet()
        }

        if(wasComplete && !isComplete) {
            completeVisits.
        }
    }

    /**
     * Updates meter registries based on SOFCompleteness data, comparing with existing data
     */
    fun updateSOFMetrics(sofCompleteness: SOFCompleteness) {
        val existingSOF = sofCompletenessDataSource.findById(sofCompleteness._id)

        // If this is a new SOF, increment total SOFs gauge
        if (existingSOF == null) {
            totalSOFs!!.incrementAndGet()
        }

        // Update pilot inbound metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.pilotInboundPresent,
            newValue = sofCompleteness.pilotInboundPresent,
            totalCounter = totalSOFPilotInbound,
            completeCounter = completeSOFPilotInbound
        )

        // Update pilot outbound metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.pilotOutboundPresent,
            newValue = sofCompleteness.pilotOutboundPresent,
            totalCounter = totalSOFPilotOutbound,
            completeCounter = completeSOFPilotOutbound
        )

        // Update tug present metrics only if the field changed
        updateFieldMetric(
            existingValue = existingSOF?.anyTugPresent,
            newValue = sofCompleteness.anyTugPresent,
            totalCounter = totalSOFAnyTugPresent,
            completeCounter = completeSOFAnyTugPresent
        )

        // Check if overall SOF completeness changed
        val wasComplete = existingSOF?.let {
            it.pilotInboundPresent &&
            it.pilotOutboundPresent &&
            it.anyTugPresent
        } ?: false

        val isComplete = sofCompleteness.pilotInboundPresent &&
                        sofCompleteness.pilotOutboundPresent &&
                        sofCompleteness.anyTugPresent

        // Update complete SOFs gauge based on completeness change
        if (!wasComplete && isComplete) {
            completeSOFs!!.incrementAndGet()
        } else if (wasComplete && !isComplete) {
            completeSOFs!!.decrementAndGet()
        }
    }

    /**
     * Helper method to update visit field metrics when the field value changes
     */
    private fun updateFieldMetric(
        existingValue: Boolean?,
        newValue: Boolean,
        totalGauge: java.util.concurrent.atomic.AtomicLong?,
        completeGauge: java.util.concurrent.atomic.AtomicLong?
    ) {
        // If this is a new field (existingValue is null), always increment total
        if (existingValue == null) {
            totalGauge?.incrementAndGet()
            if (newValue) {
                completeGauge?.incrementAndGet()
            }
        } else {
            // Update based on value changes
            if (!existingValue && newValue) {
                // Changed from false to true
                completeGauge?.incrementAndGet()
            } else if (existingValue && !newValue) {
                // Changed from true to false
                completeGauge?.decrementAndGet()
            }
        }
    }

    /**
     * Helper method to update SOF field metrics when the field value changes
     */
    private fun updateSOFFieldMetric(
        existingValue: Boolean?,
        newValue: Boolean,
        totalGauge: java.util.concurrent.atomic.AtomicLong?,
        completeGauge: java.util.concurrent.atomic.AtomicLong?
    ) {
        // If this is a new field (existingValue is null), always increment total
        if (existingValue == null) {
            totalGauge?.incrementAndGet()
            if (newValue) {
                completeGauge?.incrementAndGet()
            }
        } else {
            // Update based on value changes
            if (!existingValue && newValue) {
                // Changed from false to true
                completeGauge?.incrementAndGet()
            } else if (existingValue && !newValue) {
                // Changed from true to false
                completeGauge?.decrementAndGet()
            }
        }
    }

    /**
     * Deletes a visit from all metric gauges
     */
    fun deleteVisitMetrics(visitId: String) {
        val existingVisit = visitCompletenessDataSource.findById(visitId)
        if (existingVisit != null) {
            // Decrement total visits
            totalVisits!!.decrementAndGet()

            // Decrement field-specific totals
            totalVisitStartEndTime!!.decrementAndGet()
            totalVisitMainPortStartEnd!!.decrementAndGet()
            totalVisitSubPortStartEnd!!.decrementAndGet()

            // Decrement complete counters if the fields were complete
            if (existingVisit.startEndTimestampPresent) {
                completeVisitStartEndTime!!.decrementAndGet()
            }
            if (existingVisit.mainPortAreaStartEndTimestampPresent) {
                completeVisitMainPortStartEnd!!.decrementAndGet()
            }
            if (existingVisit.subPortAreaStartEndTimestampPresent) {
                completeVisitSubPortStartEnd!!.decrementAndGet()
            }

            // Decrement complete visits if it was complete
            val wasComplete = existingVisit.startEndTimestampPresent &&
                            existingVisit.mainPortAreaStartEndTimestampPresent &&
                            existingVisit.subPortAreaStartEndTimestampPresent
            if (wasComplete) {
                completeVisits!!.decrementAndGet()
            }
        }
    }

    /**
     * Deletes a SOF from all metric gauges
     */
    fun deleteSOFMetrics(sofId: String) {
        val existingSOF = sofCompletenessDataSource.findById(sofId)
        if (existingSOF != null) {
            // Decrement total SOFs
            totalSOFs!!.decrementAndGet()

            // Decrement field-specific totals
            totalSOFPilotInbound!!.decrementAndGet()
            totalSOFPilotOutbound!!.decrementAndGet()
            totalSOFAnyTugPresent!!.decrementAndGet()

            // Decrement complete counters if the fields were complete
            if (existingSOF.pilotInboundPresent) {
                completeSOFPilotInbound!!.decrementAndGet()
            }
            if (existingSOF.pilotOutboundPresent) {
                completeSOFPilotOutbound!!.decrementAndGet()
            }
            if (existingSOF.anyTugPresent) {
                completeSOFAnyTugPresent!!.decrementAndGet()
            }

            // Decrement complete SOFs if it was complete
            val wasComplete = existingSOF.pilotInboundPresent &&
                            existingSOF.pilotOutboundPresent &&
                            existingSOF.anyTugPresent
            if (wasComplete) {
                completeSOFs!!.decrementAndGet()
            }
        }
    }
}